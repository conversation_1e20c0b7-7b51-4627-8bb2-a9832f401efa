<?php

/**
 * Enhanced Laravel Development Server Router
 * Fixes broken pipe errors (errno=32) with comprehensive error handling
 */

$publicPath = getcwd();

$uri = urldecode(
    parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH) ?? ''
);

// This file allows us to emulate Apache's "mod_rewrite" functionality from the
// built-in PHP web server. This provides a convenient way to test a Laravel
// application without having installed a "real" web server software here.
if ($uri !== '/' && file_exists($publicPath . $uri)) {
    return false;
}

// Ignore SIGPIPE signals to prevent broken pipe errors
if (function_exists('pcntl_signal')) {
    pcntl_signal(SIGPIPE, SIG_IGN);
}

// Set error handler to suppress broken pipe errors
set_error_handler(function ($errno, $errstr, $errfile, $errline) {
    // Suppress broken pipe and related errors
    if (
        strpos($errstr, 'Broken pipe') !== false ||
        strpos($errstr, 'errno=32') !== false ||
        ($errno === E_WARNING && strpos($errstr, 'file_put_contents') !== false && strpos($errstr, 'php://stdout') !== false)
    ) {
        return true; // Suppress the error
    }
    return false; // Let other errors through
});

$formattedDateTime = date('D M j H:i:s Y');
$requestMethod = $_SERVER['REQUEST_METHOD'];
$remoteAddress = $_SERVER['REMOTE_ADDR'] . ':' . $_SERVER['REMOTE_PORT'];

// Safe logging with multiple fallback methods
$logMessage = "[$formattedDateTime] $remoteAddress [$requestMethod] URI: $uri\n";

// Method 1: Try stdout with error checking
$logged = false;
try {
    if (is_resource(STDOUT) && !feof(STDOUT)) {
        $result = @file_put_contents('php://stdout', $logMessage);
        if ($result !== false) {
            $logged = true;
        }
    }
} catch (Exception $e) {
    // Continue to fallback methods
}

// Method 2: Try stderr as fallback
if (!$logged) {
    try {
        @file_put_contents('php://stderr', $logMessage);
        $logged = true;
    } catch (Exception $e) {
        // Continue to next fallback
    }
}

// Method 3: Log to file as last resort (only if storage directory exists)
if (!$logged && is_dir($publicPath . '/storage/logs')) {
    try {
        $logFile = $publicPath . '/storage/logs/server.log';
        @file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    } catch (Exception $e) {
        // Silently fail - don't break the request
    }
}

require_once $publicPath . '/index.php';
