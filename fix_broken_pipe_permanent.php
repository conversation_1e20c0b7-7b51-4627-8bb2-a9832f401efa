<?php

/**
 * Permanent Broken Pipe Fix Script
 * Applies the fix to <PERSON><PERSON>'s server.php and creates backup/restore functionality
 */

echo "🔧 Laravel Broken Pipe Permanent Fix\n";
echo "====================================\n\n";

$serverFile = 'vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php';
$backupFile = $serverFile . '.backup';

// Check if Laravel server file exists
if (!file_exists($serverFile)) {
    echo "❌ Laravel server file not found: $serverFile\n";
    exit(1);
}

// Create backup if it doesn't exist
if (!file_exists($backupFile)) {
    echo "📋 Creating backup of original server.php...\n";
    if (copy($serverFile, $backupFile)) {
        echo "✅ Backup created: $backupFile\n";
    } else {
        echo "❌ Failed to create backup\n";
        exit(1);
    }
} else {
    echo "ℹ️  Backup already exists: $backupFile\n";
}

// Check if fix is already applied
$currentContent = file_get_contents($serverFile);
if (strpos($currentContent, 'Enhanced Laravel Development Server Router') !== false) {
    echo "✅ Broken pipe fix is already applied!\n";
} else {
    echo "⚠️  Fix not applied. Please run the str-replace-editor command to apply the fix.\n";
}

// Create a composer post-update script to reapply the fix
$composerScript = [
    'scripts' => [
        'post-update-cmd' => [
            'php fix_broken_pipe_permanent.php --restore'
        ]
    ]
];

echo "\n💡 To automatically reapply this fix after composer updates, add this to composer.json:\n";
echo json_encode($composerScript, JSON_PRETTY_PRINT) . "\n\n";

// Handle restore command
if (isset($argv[1]) && $argv[1] === '--restore') {
    echo "🔄 Restoring broken pipe fix after composer update...\n";
    
    // Read the fixed content from this script
    $fixedContent = '<?php

/**
 * Enhanced Laravel Development Server Router
 * Fixes broken pipe errors (errno=32) with comprehensive error handling
 */

$publicPath = getcwd();

$uri = urldecode(
    parse_url($_SERVER[\'REQUEST_URI\'], PHP_URL_PATH) ?? \'\'
);

// This file allows us to emulate Apache\'s "mod_rewrite" functionality from the
// built-in PHP web server. This provides a convenient way to test a Laravel
// application without having installed a "real" web server software here.
if ($uri !== \'/\' && file_exists($publicPath . $uri)) {
    return false;
}

// Ignore SIGPIPE signals to prevent broken pipe errors
if (function_exists(\'pcntl_signal\')) {
    pcntl_signal(SIGPIPE, SIG_IGN);
}

// Set error handler to suppress broken pipe errors
set_error_handler(function ($errno, $errstr, $errfile, $errline) {
    // Suppress broken pipe and related errors
    if (
        strpos($errstr, \'Broken pipe\') !== false ||
        strpos($errstr, \'errno=32\') !== false ||
        ($errno === E_WARNING && strpos($errstr, \'file_put_contents\') !== false && strpos($errstr, \'php://stdout\') !== false)
    ) {
        return true; // Suppress the error
    }
    return false; // Let other errors through
});

$formattedDateTime = date(\'D M j H:i:s Y\');
$requestMethod = $_SERVER[\'REQUEST_METHOD\'];
$remoteAddress = $_SERVER[\'REMOTE_ADDR\'] . \':\' . $_SERVER[\'REMOTE_PORT\'];

// Safe logging with multiple fallback methods
$logMessage = "[$formattedDateTime] $remoteAddress [$requestMethod] URI: $uri\n";

// Method 1: Try stdout with error checking
$logged = false;
try {
    if (is_resource(STDOUT) && !feof(STDOUT)) {
        $result = @file_put_contents(\'php://stdout\', $logMessage);
        if ($result !== false) {
            $logged = true;
        }
    }
} catch (Exception $e) {
    // Continue to fallback methods
}

// Method 2: Try stderr as fallback
if (!$logged) {
    try {
        @file_put_contents(\'php://stderr\', $logMessage);
        $logged = true;
    } catch (Exception $e) {
        // Continue to next fallback
    }
}

// Method 3: Log to file as last resort (only if storage directory exists)
if (!$logged && is_dir($publicPath . \'/storage/logs\')) {
    try {
        $logFile = $publicPath . \'/storage/logs/server.log\';
        @file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    } catch (Exception $e) {
        // Silently fail - don\'t break the request
    }
}

require_once $publicPath . \'/index.php\';
';

    if (file_put_contents($serverFile, $fixedContent)) {
        echo "✅ Broken pipe fix restored successfully!\n";
    } else {
        echo "❌ Failed to restore fix\n";
        exit(1);
    }
}

echo "🎯 Broken pipe fix management completed!\n";
