/* Hide license warning messages - specific targeting */
.alert-license,
[data-bb-toggle="authorized-reminder"],
.alert.alert-warning.bg-warning.text-white.alert-license.alert-sticky {
    display: none !important;
}

/* Hide license form sections */
#license-form,
form#license-form {
    display: none !important;
}

/* Hide license activation modal */
#quick-activation-license-modal {
    display: none !important;
}

/* Hide "Add New Plugin" menu item */
#cms-core-plugins-marketplace,
a[href*="/admin/plugins/new"] {
    display: none !important;
}

/* Hide license section in settings */
.row.mb-5.d-block.d-md-flex:has(#license-form) {
    display: none !important;
}

/* Hide license settings section specifically */
.col-12.col-md-3:has(h2:contains("License")) {
    display: none !important;
}

.col-12.col-md-3:has(h2:contains("License")) + .col-12.col-md-9 {
    display: none !important;
}

/* Hide any alert containing license text */
.alert:contains("Your license is invalid"),
.alert:contains("Please activate your license"),
.alert-warning:contains("license") {
    display: none !important;
}

/* Additional specific targeting for license elements */
.alert.alert-warning.bg-warning.text-white.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important {
    display: none !important;
}

/* Hide any element with license-related data attributes */
[data-bb-toggle*="license"],
[data-bb-toggle*="authorized"] {
    display: none !important;
}

/* Hide license modal and related elements */
.modal[id*="license"],
.modal[id*="activation"] {
    display: none !important;
}

/* Hide any button or link related to license activation */
.btn[data-bs-target*="license"],
a[href*="license"],
button[data-bs-target*="license"] {
    display: none !important;
}
