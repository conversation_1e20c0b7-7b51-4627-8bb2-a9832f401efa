<?php

/**
 * Custom Laravel Development Server Router
 * Fixes broken pipe errors (errno=32) by implementing proper error handling
 */

$publicPath = getcwd();

$uri = urldecode(
    parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH) ?? ''
);

// This file allows us to emulate Apache's "mod_rewrite" functionality from the
// built-in PHP web server. This provides a convenient way to test a Laravel
// application without having installed a "real" web server software here.
if ($uri !== '/' && file_exists($publicPath.$uri)) {
    return false;
}

// Ignore SIGPIPE signals to prevent broken pipe errors
if (function_exists('pcntl_signal')) {
    pcntl_signal(SIGPIPE, SIG_IGN);
}

$formattedDateTime = date('D M j H:i:s Y');
$requestMethod = $_SERVER['REQUEST_METHOD'];
$remoteAddress = $_SERVER['REMOTE_ADDR'].':'.$_SERVER['REMOTE_PORT'];

// Safe logging with error handling to prevent broken pipe
$logMessage = "[$formattedDateTime] $remoteAddress [$requestMethod] URI: $uri\n";

// Multiple fallback methods to handle logging safely
try {
    // Method 1: Check if stdout is still available
    if (is_resource(STDOUT) && !feof(STDOUT)) {
        $result = @file_put_contents('php://stdout', $logMessage);
        if ($result === false) {
            throw new Exception('stdout write failed');
        }
    } else {
        throw new Exception('stdout not available');
    }
} catch (Exception $e) {
    // Method 2: Try stderr as fallback
    try {
        @file_put_contents('php://stderr', $logMessage);
    } catch (Exception $e2) {
        // Method 3: Log to file as last resort
        try {
            $logFile = $publicPath . '/storage/logs/server.log';
            if (is_dir(dirname($logFile))) {
                @file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
            }
        } catch (Exception $e3) {
            // Silently fail - don't break the request
        }
    }
}

// Set error handler to catch any other pipe-related errors
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    // Ignore broken pipe errors and similar
    if (strpos($errstr, 'Broken pipe') !== false || 
        strpos($errstr, 'errno=32') !== false ||
        $errno === E_WARNING && strpos($errstr, 'file_put_contents') !== false) {
        return true; // Suppress the error
    }
    return false; // Let other errors through
});

require_once $publicPath.'/index.php';
